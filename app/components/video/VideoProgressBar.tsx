import { useRef, type MouseEvent as ReactMouseEvent } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { formatTime } from "~/utils/videoUtils";

interface VideoProgressBarProps {
  currentTime: number;
  duration: number;
  isSeeking: boolean;
  seekingTime: number;
  onSeekStart: (
    e: ReactMouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
  ) => void;
  onSeekMove: (clientX: number) => void;
  onSeekEnd: (clientX: number) => void;
  onProgressClick: (e: ReactMouseEvent<HTMLDivElement>) => void;
  className?: string;
}

export default function VideoProgressBar({
  currentTime,
  duration,
  isSeeking,
  seekingTime,
  onSeekStart,
  onSeekMove,
  onSeekEnd,
  onProgressClick,
  className = "",
}: VideoProgressBarProps) {
  const progressBarRef = useRef<HTMLDivElement>(null);

  // Calculate progress percentage - use seeking time when seeking for immediate response
  const displayTime = isSeeking ? seekingTime : currentTime;
  const progressPercentage = duration > 0 ? (displayTime / duration) * 100 : 0;

  // Specific handler for touch start
  const handleTouchSeekStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // Prevent default touch actions like scrolling
    if (e.cancelable) {
      e.preventDefault();
    }
    onSeekStart(e);
  };

  // Specific handler for touch move
  const handleTouchSeekMove = (e: React.TouchEvent<HTMLDivElement>) => {
    onSeekMove(e.touches[0].clientX);
  };

  // Specific handler for touch end
  const handleTouchSeekEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    onSeekEnd(e.changedTouches[0].clientX);
  };

  // Mouse down handler
  const handleMouseDown = (e: ReactMouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    onSeekStart(e);
  };

  return (
    <>
      {/* Center Time Display Overlay - Bigger display when seeking */}
      <AnimatePresence>
        {isSeeking && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            className="absolute inset-0 flex items-center justify-center z-30 pointer-events-none"
          >
            {/* Blur/Opacity Background */}
            <div className="absolute inset-0 bg-black/30"></div>

            {/* Time Display */}
            <div className="relative rounded-2xl py-6 w-full flex flex-row items-center justify-center">
              <div className="text-white text-xl md:text-2xl font-bold tabular-nums tracking-wider">
                {formatTime(displayTime)}
              </div>
              <div className="text-white/70 text-lg md:text-xl font-medium tabular-nums text-center">
                / {formatTime(duration)}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div
        className={`flex-1 flex items-center sm:mx-3 relative group ${className}`}
      >
        {/* Visual Timeline Wrapper (with ref for calculations) */}
        <div ref={progressBarRef} className="w-full" data-progress-bar>
          {/* Actual Visual Timeline elements */}
          <div
            className={`relative w-full rounded-full transition-all duration-150 ${
              isSeeking ? "h-2 bg-white/70" : "h-0.5 bg-white/50"
            }`}
          >
            <div
              className={`absolute top-0 left-0 h-full rounded-full transition-all duration-150 ${
                isSeeking ? "bg-white shadow-lg shadow-white/30" : "bg-white"
              }`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
            {/* Scrubber - visibility controlled by 'group-hover' on the overlay */}
            <div
              className={`absolute z-30 top-1/2 bg-white rounded-full -translate-y-1/2 -translate-x-1/2 ${
                isSeeking
                  ? "opacity-100 scale-150 h-4 w-4 shadow-lg shadow-white/50"
                  : "opacity-0 group-hover:opacity-100 group-hover:scale-125 h-2.5 w-2.5 transition-all duration-150"
              }`}
              style={{ left: `${progressPercentage}%` }}
            ></div>
            {isSeeking && (
              <div
                className="absolute bottom-full mb-2 px-1.5 py-0.5 bg-black/70 text-white text-[10px] rounded-sm font-semibold whitespace-nowrap tabular-nums transform -translate-x-1/2"
                style={{ left: `${progressPercentage}%` }}
              >
                {formatTime(displayTime)}
              </div>
            )}
          </div>
        </div>

        {/* Invisible Interaction Overlay - Reduced height to avoid overlapping with description */}
        <div
          className="absolute z-50 left-0 right-0 w-full cursor-pointer touch-none" // Interaction area for seeking
          style={{
            height: "2rem", // Reduced from 10rem to 2rem to avoid covering description area
            top: "50%",
            transform: "translateY(-50%)",
          }}
          onClick={onProgressClick}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchSeekStart}
          onTouchMove={handleTouchSeekMove}
          onTouchEnd={handleTouchSeekEnd}
        ></div>
      </div>
    </>
  );
}
