import React, { useRef, useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { formatTime } from "~/utils/videoUtils";

interface VideoProgressBarProps {
  currentTime: number;
  duration: number;
  isSeeking: boolean;
  seekingTime: number;
  onProgressClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  onSeekStart: (e: React.MouseEvent<HTMLDivElement>) => void;
  onTouchSeekStart: (e: React.TouchEvent<HTMLDivElement>) => void;
  onTouchSeekMove: (e: React.TouchEvent<HTMLDivElement>) => void;
  onTouchSeekEnd: (e: React.TouchEvent<HTMLDivElement>) => void;
  isVisible?: boolean;
  containerSelector?: string; // CSS selector for the portal container
}

const VideoProgressBar: React.FC<VideoProgressBarProps> = ({
  currentTime,
  duration,
  isSeeking,
  seekingTime,
  onProgressClick,
  onSeekStart,
  onTouchSeekStart,
  onTouchSeekMove,
  onTouchSeekEnd,
  isVisible = true,
  containerSelector = "body",
}) => {
  const progressBarRef = useRef<HTMLDivElement>(null);
  const [portalContainer, setPortalContainer] = useState<Element | null>(null);

  useEffect(() => {
    const container = document.querySelector(containerSelector);
    setPortalContainer(container);
  }, [containerSelector]);

  if (!portalContainer || !isVisible) {
    return null;
  }

  // Calculate progress percentage - use seeking time when seeking for immediate response
  const displayTime = isSeeking ? seekingTime : currentTime;
  const progressPercentage = duration > 0 ? (displayTime / duration) * 100 : 0;

  const progressBarContent = (
    <div className="fixed bottom-12 left-0 right-0 z-50 px-4">
      {/* Progress Bar Container */}
      <div className="relative group">
        {/* Visual Timeline Wrapper */}
        <div ref={progressBarRef} className="w-full">
          {/* Actual Visual Timeline elements */}
          <div
            className={`relative w-full rounded-full transition-all duration-150 ${
              isSeeking ? "h-2 bg-white/70" : "h-0.5 bg-white/50"
            }`}
          >
            <div
              className={`absolute top-0 left-0 h-full rounded-full transition-all duration-150 ${
                isSeeking
                  ? "bg-white shadow-lg shadow-white/30"
                  : "bg-white"
              }`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
            
            {/* Scrubber */}
            <div
              className={`absolute z-[70] top-1/2 bg-white rounded-full -translate-y-1/2 -translate-x-1/2 ${
                isSeeking
                  ? "opacity-100 scale-150 h-4 w-4 shadow-lg shadow-white/50"
                  : "opacity-0 group-hover:opacity-100 group-hover:scale-125 h-2.5 w-2.5 transition-all duration-150"
              }`}
              style={{ left: `${progressPercentage}%` }}
            ></div>
            
            {/* Time tooltip when seeking */}
            {isSeeking && (
              <div
                className="absolute bottom-full mb-2 px-1.5 py-0.5 bg-black/70 text-white text-[10px] rounded-sm font-semibold whitespace-nowrap tabular-nums transform -translate-x-1/2 z-[70]"
                style={{ left: `${progressPercentage}%` }}
              >
                {formatTime(displayTime)}
              </div>
            )}
          </div>
        </div>

        {/* Invisible Interaction Overlay */}
        <div
          className="absolute z-[70] left-0 right-0 w-full cursor-pointer touch-none"
          style={{
            height: "3rem",
            top: "50%",
            transform: "translateY(-50%)",
          }}
          onClick={onProgressClick}
          onMouseDown={onSeekStart}
          onTouchStart={onTouchSeekStart}
          onTouchMove={onTouchSeekMove}
          onTouchEnd={onTouchSeekEnd}
        ></div>
      </div>

      {/* Time Display */}
      <div className="flex justify-between items-center mt-2 px-2">
        <span className="text-white text-[10px] tabular-nums">
          {formatTime(currentTime)}
        </span>
        <span className="text-white text-[10px] tabular-nums">
          {formatTime(duration)}
        </span>
      </div>
    </div>
  );

  return createPortal(progressBarContent, portalContainer);
};

export default VideoProgressBar;
