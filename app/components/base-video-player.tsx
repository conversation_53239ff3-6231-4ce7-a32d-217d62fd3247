import {
  useState,
  useRef,
  useEffect,
  type MouseEvent as ReactMouse<PERSON>vent,
  useCallback,
} from "react";
import Hls from "hls.js"; // Import HLS.js
import { Play, Pause, Maximize2, Subtitles } from "lucide-react";
import { AnimatePresence, motion } from "framer-motion";
import * as Toolt<PERSON> from "@radix-ui/react-tooltip"; // Import Radix Tooltip
import VideoLanguageSelector from "~/components/ui/VideoLanguageSelector";
import { parseVTT, formatTime, type VttCue } from "~/utils/videoUtils"; // Import from new util file
import { useTranslation } from "react-i18next"; // Import useTranslation
import LockedVideoOverlay from "~/components/video/LockedVideoOverlay";
import VideoProgressBar from "~/components/video/VideoProgressBar";

interface BaseVideoPlayerProps {
  src: string;
  poster?: string;
  subtitleUrl?: string; // URL to WebVTT subtitle file
  subtitleLabel?: string; // Optional label for the subtitle track
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  showControls?: boolean; // Controls visibility managed by parent
  showTitleDescription?: boolean;
  isLocked?: boolean; // New prop to indicate if the video is locked
  isActive?: boolean; // New prop to indicate if this video is currently active
  controlsBottomPadding?: string; // Ensure this prop is defined

  title?: string; // Video title
  description?: string; // Video description
  tags?: string[]; // Video tags
  onPlay?: () => void;
  onPause?: () => void;
  onTimeUpdate?: (currentTime: number) => void;
  onLoadedMetadata?: (duration: number) => void;
  onEnded?: () => void;
  onTopUpClick?: () => void; // Callback for top-up button in locked overlay
  onNavigateClick?: () => void; // Callback for navigation when locked video is clicked
  // Pass through other video attributes if needed
  [key: string]: any; // Allow passthrough props like className
}

export default function BaseVideoPlayer({
  src,
  poster,
  subtitleUrl,
  subtitleLabel = "English",
  autoPlay = true,
  loop = false,
  muted = false,
  showControls = true, // Default to true if not provided
  showTitleDescription = true,
  isLocked = false, // Default to false
  isActive = true, // Default to true if not provided
  controlsBottomPadding = "", // Correctly destructure with default
  videoId, // Video/Post ID for unlocking

  title = "", // Default to empty string
  description = "", // Default to empty string
  tags = [], // Default to empty array
  onPlay,
  onPause,
  onTimeUpdate,
  onLoadedMetadata,
  onEnded,
  onTopUpClick,
  onNavigateClick,
  ...props // Pass rest of the props to the video element
}: BaseVideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(autoPlay && !isLocked); // Don't autoplay if locked
  const { t } = useTranslation(); // Get t function
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isSeeking, setIsSeeking] = useState(false);
  const [seekingTime, setSeekingTime] = useState(0); // Separate state for seeking position
  const [showMore, setShowMore] = useState(false); // For description expand/collapse
  const [showCenterIcon, setShowCenterIcon] = useState(false);
  const [centerIconType, setCenterIconType] = useState<"play" | "pause">(
    "play"
  );
  // Remove native subtitle state
  // const [subtitlesEnabled, setSubtitlesEnabled] = useState(\n  //   subtitleUrl ? true : false\n  // );

  // State for custom subtitles
  const [parsedCues, setParsedCues] = useState<VttCue[]>([]);
  const [currentSubtitle, setCurrentSubtitle] = useState<string>("");
  const [subtitlesVisible, setSubtitlesVisible] = useState(!!subtitleUrl); // New state for subtitle visibility

  const centerIconTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null); // Ref for HLS.js instance

  // Effect to clear timeout on unmount
  useEffect(() => {
    return () => {
      if (centerIconTimeoutRef.current) {
        clearTimeout(centerIconTimeoutRef.current);
      }
    };
  }, []);

  // Reset video time and custom subtitle state when src changes
  useEffect(() => {
    // Reset states when video source changes
    setCurrentTime(0);
    setDuration(0);
    setShowMore(false);
    setIsPlaying(autoPlay && !isLocked);
    setCurrentSubtitle(""); // Reset subtitle text

    if (videoRef.current) {
      const videoElement = videoRef.current;

      // Force reset video time to 0 - important for episode switching
      videoElement.currentTime = 0;

      // Pause the video first to ensure clean state
      videoElement.pause();

      // Clean up previous HLS instance if it exists
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      if (src && src.includes(".m3u8")) {
        if (Hls.isSupported()) {
          const hls = new Hls();
          hlsRef.current = hls;
          hls.loadSource(src);
          hls.attachMedia(videoElement);
          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            if (autoPlay && !isLocked) {
              videoElement.play().catch((error) => {
                console.error("HLS.js: Error attempting to autoplay:", error);
                setIsPlaying(false);
              });
            }
          });
          // Optional: More HLS event handling (e.g., Hls.Events.ERROR)
          hls.on(Hls.Events.ERROR, function (_, data) {
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  console.error("HLS.js fatal network error encountered", data);
                  // Try to recover network error
                  hls.startLoad();
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  console.error("HLS.js fatal media error encountered", data);
                  hls.recoverMediaError();
                  break;
                default:
                  // Cannot recover
                  console.error("HLS.js fatal error, cannot recover", data);
                  hls.destroy();
                  hlsRef.current = null;
                  break;
              }
            } else {
              console.warn("HLS.js non-fatal error:", data);
            }
          });
        } else if (videoElement.canPlayType("application/vnd.apple.mpegurl")) {
          // For Safari and other browsers that support HLS natively
          videoElement.src = src;
          videoElement.addEventListener("loadedmetadata", () => {
            if (autoPlay && !isLocked) {
              videoElement.play().catch((error) => {
                console.error(
                  "Native HLS: Error attempting to autoplay:",
                  error
                );
                setIsPlaying(false);
              });
            }
          });
        } else {
          console.error("HLS.js is not supported on this browser.");
        }
      } else {
        // For non-M3U8 sources
        videoElement.src = src; // Set src directly for MP4, WebM etc.
        if (autoPlay && !isLocked) {
          videoElement.play().catch((e) => {
            console.error("Error auto-playing non-HLS video:", e);
            setIsPlaying(false);
          });
        }
      }
    }

    // Cleanup function
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src, autoPlay, isLocked]);

  // Fetch and parse VTT file when subtitleUrl changes
  useEffect(() => {
    if (!subtitleUrl) {
      setParsedCues([]); // Clear cues if no URL
      setCurrentSubtitle(""); // Clear current text
      return;
    }

    fetch(subtitleUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
      })
      .then((vttText) => {
        const cues = parseVTT(vttText);
        setParsedCues(cues);
      })
      .catch((error) => {
        console.error("Error fetching or parsing VTT file:", error);
        setParsedCues([]); // Clear cues on error
        setCurrentSubtitle("");
      });

    // Cleanup: Reset cues when URL changes or component unmounts
    return () => {
      setParsedCues([]);
      setCurrentSubtitle("");
    };
  }, [subtitleUrl]);

  // Additional effect to ensure video always starts from 0 when src changes
  // This handles cases where the component doesn't remount but src changes
  useEffect(() => {
    if (videoRef.current && src) {
      const videoElement = videoRef.current;

      // Add event listener for when video is loaded and ready
      const handleLoadedData = () => {
        // Ensure video starts from beginning
        if (videoElement.currentTime !== 0) {
          videoElement.currentTime = 0;
          setCurrentTime(0);
        }
      };

      videoElement.addEventListener("loadeddata", handleLoadedData);

      // Cleanup
      return () => {
        videoElement.removeEventListener("loadeddata", handleLoadedData);
      };
    }
  }, [src]); // Only depend on src to trigger when video URL changes

  // Reset video time to 0 when becoming active (for episode switching)
  useEffect(() => {
    if (isActive && videoRef.current) {
      const videoElement = videoRef.current;

      // Reset video time to 0 when this video becomes active
      if (videoElement.currentTime !== 0) {
        videoElement.currentTime = 0;
        setCurrentTime(0);
      }

      // Pause other videos and ensure this one can play if autoPlay is enabled
      if (autoPlay && !isLocked) {
        videoElement.play().catch((error) => {
          console.error(
            "Error attempting to play video when becoming active:",
            error
          );
          setIsPlaying(false);
        });
      }
    } else if (!isActive && videoRef.current) {
      // Pause video when becoming inactive
      const videoElement = videoRef.current;
      videoElement.pause();
      setIsPlaying(false);
    }
  }, [isActive, autoPlay, isLocked]); // Trigger when isActive changes

  // --- Play/Pause ---
  const togglePlayPause = useCallback(() => {
    // Allow play/pause even for locked videos since overlay no longer blocks interaction
    // The lock state is now indicated by the overlay, but basic controls still work
    if (videoRef.current) {
      const video = videoRef.current;
      let nextIcon: "play" | "pause";

      if (videoRef.current.paused || videoRef.current.ended) {
        video.play().catch((error) => {
          console.error("Error attempting to play video:", error);
          setIsPlaying(false); // Ensure state is correct if play fails
        });
        nextIcon = "play";
      } else {
        video.pause();
        nextIcon = "pause";
      }

      // Show center icon briefly
      setCenterIconType(nextIcon === "play" ? "pause" : "play");
      setShowCenterIcon(true);
      if (centerIconTimeoutRef.current) {
        clearTimeout(centerIconTimeoutRef.current);
      }
      centerIconTimeoutRef.current = setTimeout(() => {
        setShowCenterIcon(false);
      }, 1000); // Show for 500ms
    }
  }, [isLocked, centerIconTimeoutRef]);

  // --- Event Handlers ---
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    onPlay?.();
  }, [onPlay]);

  const handlePause = useCallback(() => {
    setIsPlaying(false);
    onPause?.();
  }, [onPause]);

  const handleTimeUpdateInternal = useCallback(() => {
    // console.log(`handleTimeUpdateInternal - isSeeking: ${isSeeking}`);
    if (videoRef.current && !isSeeking) {
      const time = videoRef.current.currentTime;
      const dur = videoRef.current.duration;
      // console.log(
      //   `  -> Updating time: ${time.toFixed(2)}, Duration: ${dur.toFixed(2)}`
      // );
      setCurrentTime(time);
      if (dur && isFinite(dur) && duration !== dur) {
        console.log(`  -> Duration mismatch, updating state: ${dur}`);
        setDuration(dur);
      }
      onTimeUpdate?.(time);

      // Update custom subtitle based on current time
      let activeCueText = "";
      for (const cue of parsedCues) {
        if (time >= cue.start && time <= cue.end) {
          activeCueText = cue.text;
          break; // Found the active cue
        }
      }
      // Only update state if the text actually changed
      if (activeCueText !== currentSubtitle) {
        setCurrentSubtitle(activeCueText);
      }
    } else {
      console.log("  -> Skipping time update (isSeeking=true or no videoRef)");
    }
  }, [isSeeking, onTimeUpdate, duration, parsedCues, currentSubtitle]);

  const handleLoadedMetadataInternal = useCallback(() => {
    if (videoRef.current) {
      const dur = videoRef.current.duration;
      setDuration(dur);
      onLoadedMetadata?.(dur);
    }
  }, [onLoadedMetadata]);

  const handleEnded = useCallback(() => {
    setIsPlaying(false); // Ensure state is updated
    onEnded?.();
    // If not looping, reset time? Optionally handle replay logic here or in parent
    if (!loop && videoRef.current) {
      setCurrentTime(0); // Reset to beginning
    }
  }, [loop, onEnded]);

  // --- Seeking Logic --- (Moved to VideoProgressBar component)

  // Helper function to calculate seek time from event coordinates
  const calculateSeekTime = (
    clientX: number,
    progressBarElement: HTMLDivElement
  ): number | null => {
    if (!progressBarElement || !duration || !isFinite(duration)) return null;
    const rect = progressBarElement.getBoundingClientRect();
    const offsetX = Math.max(0, Math.min(clientX - rect.left, rect.width));
    const newTime = (offsetX / rect.width) * duration;
    return isFinite(newTime) ? newTime : null;
  };

  const handleProgressClick = (e: ReactMouseEvent<HTMLDivElement>) => {
    if (!videoRef.current) return;
    const progressBarElement = e.currentTarget.querySelector(
      "[data-progress-bar]"
    ) as HTMLDivElement;
    if (!progressBarElement) return;
    const newTime = calculateSeekTime(e.clientX, progressBarElement);
    if (newTime !== null) {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime); // Update state immediately
    }
  };

  // Combined handler for starting seek (mouse or touch)
  const handleSeekStart = (
    e: ReactMouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
  ) => {
    // Prevent text selection or default touch actions
    e.preventDefault();
    setIsSeeking(true);
    // Initialize seeking time with current time
    setSeekingTime(currentTime);
  };

  // Combined handler for updating seek position (mouse or touch)
  const handleSeekMove = useCallback(
    (clientX: number) => {
      if (!isSeeking) return;
      // Find the progress bar element in the DOM
      const progressBarElement = document.querySelector(
        "[data-progress-bar]"
      ) as HTMLDivElement;
      if (!progressBarElement) return;
      const newTime = calculateSeekTime(clientX, progressBarElement);
      if (newTime !== null) {
        // Update seeking time immediately for responsive scrubber
        setSeekingTime(newTime);
        // Also update current time for other UI elements
        setCurrentTime(newTime);
      }
    },
    [isSeeking, duration] // Dependencies: isSeeking, duration
  );

  // Combined handler for ending seek (mouse or touch)
  const handleSeekEnd = useCallback(
    (clientX: number) => {
      if (!isSeeking || !videoRef.current) return;
      // Find the progress bar element in the DOM
      const progressBarElement = document.querySelector(
        "[data-progress-bar]"
      ) as HTMLDivElement;
      if (!progressBarElement) return;
      const newTime = calculateSeekTime(clientX, progressBarElement);
      if (newTime !== null) {
        videoRef.current.currentTime = newTime; // Set the actual video time
        setCurrentTime(newTime); // Ensure state matches
        setSeekingTime(newTime); // Update seeking time as well
      }
      setIsSeeking(false);
    },
    [isSeeking, duration] // Dependencies: isSeeking, duration
  );

  // Add global listeners for mouse move/up during seek
  useEffect(() => {
    if (isSeeking) {
      const handleGlobalMouseMove = (e: globalThis.MouseEvent) =>
        handleSeekMove(e.clientX);
      const handleGlobalMouseUp = (e: globalThis.MouseEvent) =>
        handleSeekEnd(e.clientX);

      // Add mouse listeners
      window.addEventListener("mousemove", handleGlobalMouseMove);
      window.addEventListener("mouseup", handleGlobalMouseUp);

      return () => {
        // Cleanup mouse listeners
        window.removeEventListener("mousemove", handleGlobalMouseMove);
        window.removeEventListener("mouseup", handleGlobalMouseUp);
      };
    }
  }, [isSeeking, handleSeekMove, handleSeekEnd]);

  // --- Fullscreen --- (Basic Implementation)
  const toggleFullScreen = () => {
    if (!videoRef.current) return;
    if (!document.fullscreenElement) {
      videoRef.current.requestFullscreen().catch((err) => {
        alert(
          `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
        );
      });
    } else {
      document.exitFullscreen();
    }
  };

  // --- Subtitle Toggle ---
  const toggleSubtitles = () => {
    setSubtitlesVisible(!subtitlesVisible);
  };

  return (
    <div className={`relative w-full h-full  ${props.className || ""}`}>
      <video
        {...props} // Spread remaining props
        ref={videoRef}
        src={src}
        poster={poster}
        autoPlay={autoPlay} // Allow autoplay even for locked videos
        loop={loop}
        muted={muted}
        onPlay={handlePlay}
        onPause={handlePause}
        onTimeUpdate={handleTimeUpdateInternal}
        onLoadedMetadata={handleLoadedMetadataInternal}
        onEnded={handleEnded}
        onSeeking={() => setIsSeeking(true)} // Use built-in seeking events
        onSeeked={() => setIsSeeking(false)}
        className="w-full h-full object-contain sm:object-contain object-top " // Revert back to object-contain
        onClick={togglePlayPause} // Toggle play/pause on video click
        playsInline={true}
        controls={false} // Explicitly disable native controls
      >
        {/* Remove native subtitle track */}
        {/* {subtitleUrl && (\n          <track\n            src={subtitleUrl}\n            kind="subtitles"\n            srcLang="en"\n            label={subtitleLabel}\n            default={subtitlesEnabled}\n          />\n        )} */}
        Your browser does not support the video tag.
      </video>

      {/* Custom Subtitle Display Area */}
      {subtitlesVisible && currentSubtitle && (
        <div
          className="absolute left-1/2 bottom-[20%] transform -translate-x-1/2 \
                     px-4 py-2 bg-black/90 text-white text-center text-sm md:text-base rounded \
                     pointer-events-none z-20 whitespace-pre-wrap"
        >
          {currentSubtitle}
        </div>
      )}

      {/* Center Play/Pause Icon Overlay */}
      <AnimatePresence>
        {showCenterIcon && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute inset-0 flex items-center justify-center z-20 pointer-events-none"
          >
            <div className="bg-black/50 rounded-full p-4">
              {centerIconType === "play" ? (
                <Play size={48} fill="white" className="text-white" />
              ) : (
                <Pause size={48} fill="white" className="text-white" />
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* --- Controls --- */}
      {!isLocked && ( // Also hide controls if locked
        <div
          className={`absolute md:m-3 bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent z-10 transition-opacity duration-300 ${controlsBottomPadding}`}
          onClick={(e) => e.stopPropagation()} // Prevent video click through controls
        >
          {/* Video Metadata (Title, Tags, Description) */}
          <div className="mb-3 px-4 relative z-60">
            {/* Tags */}
            {tags && tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-0.5 bg-white/10 text-white text-[10px] rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* Title */}
            {showTitleDescription && title && (
              <h3 className="text-white font-semibold text-sm mb-1">{title}</h3>
            )}

            {/* Description */}
            {description && (
              <p className="text-gray-200 text-xs leading-snug max-w-prose">
                {showMore
                  ? description
                  : `${description.substring(0, 80)}${
                      description.length > 80 ? "..." : ""
                    }`}
                {description.length > 80 && (
                  <button
                    className="text-red-500 font-semibold text-xs ml-1 hover:underline focus:outline-none relative z-70 pointer-events-auto"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowMore(!showMore);
                    }}
                  >
                    {showMore ? t("video.less") : t("video.more")}
                  </button>
                )}
              </p>
            )}
          </div>

          <div className="flex items-center text-white lg:px-3">
            {/* Play/Pause Button - Hidden on sm screens and below */}
            <button className="p-1 hidden md:block" onClick={togglePlayPause}>
              {isPlaying ? (
                <Pause size={20} fill="currentColor" />
              ) : (
                <Play size={20} fill="currentColor" />
              )}
            </button>

            {/* Progress Bar Component */}
            <VideoProgressBar
              currentTime={currentTime}
              duration={duration}
              isSeeking={isSeeking}
              seekingTime={seekingTime}
              onSeekStart={handleSeekStart}
              onSeekMove={handleSeekMove}
              onSeekEnd={handleSeekEnd}
              onProgressClick={handleProgressClick}
            />

            {/* Time Display - Hidden on sm screens and below */}
            <span className="text-[10px] w-auto text-center tabular-nums whitespace-nowrap hidden sm:block">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>

            {/* Subtitle Toggle Button */}
            {subtitleUrl && (
              <Tooltip.Provider delayDuration={200}>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <button
                      className={`p-1 hidden sm:block ml-2 ${
                        subtitlesVisible ? "text-brand-red" : "text-white"
                      }`}
                      onClick={toggleSubtitles}
                      // title={ // Remove native title, replaced by Radix Tooltip
                      //   subtitlesVisible ? "Disable subtitles" : "Enable subtitles"
                      // }
                    >
                      <Subtitles size={18} />
                    </button>
                  </Tooltip.Trigger>
                  <Tooltip.Portal>
                    <Tooltip.Content
                      sideOffset={5}
                      className="z-[100] rounded-md bg-black/90 px-3 py-1.5 text-xs text-white shadow-md animate-fadeIn"
                      // data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade
                      // data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade
                      // data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade
                      // data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade
                    >
                      {subtitlesVisible
                        ? t("video.disableSubtitles")
                        : t("video.enableSubtitles")}
                      <Tooltip.Arrow className="fill-black/90" />
                    </Tooltip.Content>
                  </Tooltip.Portal>
                </Tooltip.Root>
              </Tooltip.Provider>
            )}

            {/* Language Selection Component - Keep for potential future use (e.g., loading different VTTs) */}
            {subtitleUrl && <VideoLanguageSelector />}

            {/* Fullscreen Button - Hidden on sm screens and below */}
            <button
              className="p-1 hidden sm:block md:ml-3"
              onClick={toggleFullScreen}
            >
              <Maximize2 size={20} />
            </button>
          </div>
        </div>
      )}

      {/* Locked Video Overlay - Renders if video is locked */}
      {isLocked && (
        <LockedVideoOverlay
          posterUrl={poster}
          seriesTitle={title}
          onTopUpClick={onTopUpClick}
          onNavigateClick={onNavigateClick}
        />
      )}
    </div>
  );
}
